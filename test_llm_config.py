#!/usr/bin/env python3
"""
Test script to verify the local LLM configuration is working correctly.
"""

from LLM import GPT_response

def test_llm_endpoints():
    """Test each model endpoint to ensure they're working"""
    
    # Test models from each endpoint
    test_models = [
        "qwen3-30b-a3b-mlx",    # Endpoint 1
        "qwen3-235b-a22b",      # Endpoint 1  
        "gemma-3-27b",          # Endpoint 1
        "jan-nano",             # Endpoint 2
        "qwen3:0.6b"            # Endpoint 3
    ]
    
    # Simple test message
    test_messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello! Please respond with 'Hello, I am working correctly!' to confirm you are functioning."}
    ]
    
    print("Testing Local LLM Configuration")
    print("=" * 50)
    
    for model in test_models:
        print(f"\nTesting model: {model}")
        print("-" * 30)
        
        try:
            response, token_count = GPT_response(test_messages, model)
            print(f"✓ Success!")
            print(f"Response: {response}")
            print(f"Token count: {token_count}")
            
        except Exception as e:
            print(f"✗ Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    test_llm_endpoints()
