#!/usr/bin/env python3
"""
Simple test to verify env2 and env3 work with the new LLM configuration.
This runs a minimal test with just one iteration on the smallest environment.
"""

import sys
import os

# Test env2 with minimal configuration
def test_env2():
    print("Testing env2-box-arrange.py with minimal configuration...")
    print("=" * 60)
    
    try:
        # Import the run_exp function directly from the env2 file
        import importlib.util
        spec = importlib.util.spec_from_file_location("env2_module", "./env2-box-arrange.py")
        env2_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(env2_module)
        run_exp_env2 = env2_module.run_exp
        
        # Test parameters - smallest possible configuration
        Saving_path = './test_results_env2'
        pg_row_num = 2
        pg_column_num = 2
        iteration_num = 0
        query_time_limit = 5  # Very small limit for quick test
        dialogue_history_method = '_w_only_state_action_history'
        cen_decen_framework = 'CMAS'  # Simplest framework
        
        # Create directory if it doesn't exist
        os.makedirs(Saving_path, exist_ok=True)
        
        print(f"Running env2 test with {pg_row_num}x{pg_column_num} grid...")
        
        result = run_exp_env2(
            Saving_path, 
            pg_row_num, 
            pg_column_num, 
            iteration_num, 
            query_time_limit, 
            dialogue_history_method, 
            cen_decen_framework
        )
        
        print("✓ env2 test completed successfully!")
        print(f"Result: {result[3]}")  # success_failure status
        return True
        
    except Exception as e:
        print(f"✗ env2 test failed: {str(e)}")
        return False

# Test env3 with minimal configuration  
def test_env3():
    print("\nTesting env3-box-arrange.py with minimal configuration...")
    print("=" * 60)
    
    try:
        # Import the run_exp function directly from the env3 file
        import importlib.util
        spec = importlib.util.spec_from_file_location("env3_module", "./env3-box-arrange.py")
        env3_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(env3_module)
        run_exp_env3 = env3_module.run_exp
        
        # Test parameters - smallest possible configuration
        Saving_path = './test_results_env3'
        pg_row_num = 4  # Minimum for env3
        iteration_num = 0
        query_time_limit = 5  # Very small limit for quick test
        dialogue_history_method = '_w_only_state_action_history'
        cen_decen_framework = 'CMAS'  # Simplest framework
        model_name = 'jan-nano'  # Fast model for testing
        
        # Create directory if it doesn't exist
        os.makedirs(Saving_path, exist_ok=True)
        
        print(f"Running env3 test with {pg_row_num} boxes...")
        
        result = run_exp_env3(
            Saving_path, 
            pg_row_num, 
            iteration_num, 
            query_time_limit, 
            dialogue_history_method, 
            cen_decen_framework, 
            model_name
        )
        
        print("✓ env3 test completed successfully!")
        print(f"Result: {result[3]}")  # success_failure status
        return True
        
    except Exception as e:
        print(f"✗ env3 test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("Running simplified environment tests...")
    print("This will test if the LLM configuration works with the actual environments.")
    print()
    
    # Test both environments
    env2_success = test_env2()
    env3_success = test_env3()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY:")
    print(f"env2 test: {'✓ PASSED' if env2_success else '✗ FAILED'}")
    print(f"env3 test: {'✓ PASSED' if env3_success else '✗ FAILED'}")
    
    if env2_success and env3_success:
        print("\n🎉 All tests passed! The LLM configuration is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
